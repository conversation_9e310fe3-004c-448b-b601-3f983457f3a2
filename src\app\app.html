<nav
  class="bg-surface sticky top-0 z-50 transition-all duration-300 ease-in-out"
  [class.shadow-md]="scrolled"
>
  <div class="max-w-3xl mx-auto flex items-center justify-between px-6 py-3">
    <div class="font-bold text-xl tracking-wider text-text animate-fade-in">
      MyPortfolio
    </div>
    <ul class="flex gap-8">
      <li>
        <a
          routerLink="/"
          routerLinkActive="text-primary after:scale-x-100"
          class="relative text-text-light hover:text-primary transition-colors duration-200 pb-1 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-full after:h-0.5 after:bg-primary after:transition-transform after:duration-300 after:ease-out after:scale-x-0 after:origin-left hover:after:scale-x-100"
          >Home</a
        >
      </li>
      <li>
        <a
          routerLink="/about"
          routerLinkActive="text-primary after:scale-x-100"
          class="relative text-text-light hover:text-primary transition-colors duration-200 pb-1 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-full after:h-0.5 after:bg-primary after:transition-transform after:duration-300 after:ease-out after:scale-x-0 after:origin-left hover:after:scale-x-100"
          >About</a
        >
      </li>
      <li>
        <a
          routerLink="/projects"
          routerLinkActive="text-primary after:scale-x-100"
          class="relative text-text-light hover:text-primary transition-colors duration-200 pb-1 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-full after:h-0.5 after:bg-primary after:transition-transform after:duration-300 after:ease-out after:scale-x-0 after:origin-left hover:after:scale-x-100"
          >Projects</a
        >
      </li>
      <li>
        <a
          routerLink="/contact"
          routerLinkActive="text-primary after:scale-x-100"
          class="relative text-text-light hover:text-primary transition-colors duration-200 pb-1 after:content-[''] after:absolute after:left-0 after:bottom-0 after:w-full after:h-0.5 after:bg-primary after:transition-transform after:duration-300 after:ease-out after:scale-x-0 after:origin-left hover:after:scale-x-100"
          >Contact</a
        >
      </li>
    </ul>
  </div>
</nav>
<!-- Minimal Social Links (left side, desktop only) -->
<div
  class="hidden md:flex flex-col fixed left-6 top-1/3 z-40 space-y-4 animate-slide-in-left"
>
  <a
    href="https://github.com/yourusername"
    target="_blank"
    rel="noopener"
    class="group transform transition-transform hover:-translate-y-1"
  >
    <svg
      class="w-6 h-6 text-text-light group-hover:text-primary transition-colors duration-200"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        d="M12 2C6.477 2 2 6.484 2 12.021c0 4.428 2.865 8.184 6.839 9.504.5.092.682-.217.682-.482 0-.237-.009-.868-.014-1.703-2.782.605-3.369-1.342-3.369-1.342-.454-1.154-1.11-1.462-1.11-1.462-.908-.62.069-.608.069-.608 1.004.07 1.532 1.032 1.532 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.339-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.987 1.029-2.686-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.025A9.564 9.564 0 0 1 12 6.844c.85.004 1.705.115 2.504.337 1.909-1.295 2.748-1.025 2.748-1.025.546 1.378.202 2.397.1 2.65.64.699 1.028 1.593 1.028 2.686 0 3.847-2.338 4.695-4.566 4.944.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.749 0 .267.18.577.688.479C19.138 20.2 22 16.447 22 12.021 22 6.484 17.523 2 12 2z"
      />
    </svg>
  </a>
  <a
    href="https://linkedin.com/in/yourusername"
    target="_blank"
    rel="noopener"
    class="group transform transition-transform hover:-translate-y-1"
  >
    <svg
      class="w-6 h-6 text-text-light group-hover:text-primary transition-colors duration-200"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.268c-.966 0-1.75-.784-1.75-1.75s.784-1.75 1.75-1.75 1.75.784 1.75 1.75-.784 1.75-1.75 1.75zm15.5 11.268h-3v-5.604c0-1.337-.025-3.063-1.868-3.063-1.868 0-2.154 1.459-2.154 2.968v5.699h-3v-10h2.881v1.367h.041c.401-.761 1.379-1.563 2.841-1.563 3.039 0 3.6 2.001 3.6 4.601v5.595z"
      />
    </svg>
  </a>
  <a
    href="https://x.com/yourusername"
    target="_blank"
    rel="noopener"
    class="group transform transition-transform hover:-translate-y-1"
  >
    <svg
      class="w-6 h-6 text-text-light group-hover:text-primary transition-colors duration-200"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        d="M22.162 0h-4.327l-5.835 8.228-5.835-8.228h-4.327l8.228 11.614-8.228 11.614h4.327l5.835-8.228 5.835 8.228h4.327l-8.228-11.614z"
      />
    </svg>
  </a>
  <div class="w-px h-16 bg-text-light mx-auto"></div>
</div>
<router-outlet [@routeAnimations]="true"></router-outlet>
