<section class="py-16 px-4 bg-background">
  <div class="max-w-3xl mx-auto">
    <h2
      class="text-4xl font-bold text-center text-text mb-12 animate-fade-in animate-slide-in-up"
    >
      My Projects
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <app-project-card
        *ngFor="let project of projects; let i = index"
        [project]="project"
        class="block transform transition-transform duration-300 hover:-translate-y-2 animate-fade-in"
        [ngStyle]="{ 'animation-delay': i * 0.1 + 's' }"
      ></app-project-card>
    </div>
  </div>
</section>
