/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {
      colors: {
        background: '#0F0F0F', // Very dark charcoal for main background
        surface: '#1A1A1A', // Slightly lighter dark for cards/components
        text: '#E0E0E0', // Light gray for primary text
        'text-light': '#B0B0B0', // Muted gray for secondary text
        primary: '#00FFFF', // Vibrant cyan for accents and primary actions
        'primary-dark': '#00C0C0', // Darker shade for hover/active states
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'], // Modern sans-serif font
        serif: ['Merriweather', 'serif'], // Optional: for headings or specific sections
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-out',
        'slide-in-right': 'slideInRight 0.5s ease-out',
        'slide-in-left': 'slideInLeft 0.5s ease-out',
        'slide-in-up': 'slideInUp 0.5s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        slideInRight: {
          '0%': { transform: 'translateX(20px)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-20px)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        slideInUp: {
          '0%': { transform: 'translateY(20px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
      },
    },
  },
  plugins: [],
} 