import { Component } from '@angular/core';
import { ProjectCard } from '../project-card/project-card';
import { CommonModule } from '@angular/common';

interface Project {
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}

@Component({
  selector: 'app-projects',
  imports: [ProjectCard, CommonModule],
  templateUrl: './projects.html',
  styleUrl: './projects.css',
})
export class Projects {
  projects: Project[] = [
    {
      title: 'Project One',
      description:
        'A brief description of project one, highlighting its key features.',
      imageUrl:
        'https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Project+One',
      link: '#',
    },
    {
      title: 'Project Two',
      description:
        'This project demonstrates advanced techniques in web development.',
      imageUrl:
        'https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Project+Two',
      link: '#',
    },
    {
      title: 'Project Three',
      description: 'An innovative solution built with modern web technologies.',
      imageUrl:
        'https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Project+Three',
      link: '#',
    },
    {
      title: 'Project Four',
      description: 'A creative design concept brought to life through code.',
      imageUrl:
        'https://via.placeholder.com/400x250/F3F4F6/6B7280?text=Project+Four',
      link: '#',
    },
  ];
}
