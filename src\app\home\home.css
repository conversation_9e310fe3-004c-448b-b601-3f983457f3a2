.landing {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}
.intro {
  text-align: center;
  padding: 2rem 1rem;
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.07);
  max-width: 420px;
  margin: 0 auto;
}
.intro h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #222;
}
.intro .highlight {
  color: #0078d7;
  transition: color 0.3s;
}
.intro .subtitle {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 1rem;
}
.intro .desc {
  color: #666;
  margin-bottom: 2rem;
}
.cta-btn {
  display: inline-block;
  padding: 0.7rem 2rem;
  background: #0078d7;
  color: #fff;
  border-radius: 2rem;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0, 120, 215, 0.08);
  transition: background 0.2s, transform 0.2s;
}
.cta-btn:hover {
  background: #005fa3;
  transform: translateY(-2px) scale(1.04);
}
.fade-in {
  opacity: 0;
  animation: fadeIn 1.2s ease-in-out 0.2s forwards;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
